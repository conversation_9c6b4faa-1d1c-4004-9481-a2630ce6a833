import pygame,random,math

fullscreen_resolution = 1366,768

gamescreen = pygame.display.set_mode(fullscreen_resolution)

gameclock = pygame.time.Clock()

mouse = {"x":None,"y":None,"pressed":[False,False,False]}
keylist = []
enemies = []
enemycooldown = 0
maxenemycooldown = 15
maxenemies = 50
enemypadding = 200
enemyspeed = 2
invistimer = 0
maxinvistimer = 300
closestenemy = None
rotated_weapon = None

swordimg = pygame.image.load("sword.png").convert_alpha()
swordimg = pygame.transform.scale(swordimg,(64,64))

gunimg = pygame.image.load("gun.png").convert_alpha()
gunimg = pygame.transform.scale(gunimg,(64,64))

enemyimgright = pygame.image.load("zombielive.png").convert_alpha()
enemyimgleft = pygame.transform.flip(enemyimgright,True,False)


def invertcolor(color):
    return (255-color[0],255-color[1],255-color[2])

def invertimage(image):
    return

def inbounds(object):
    return -object.width < object.x < fullscreen_resolution[0] and -object.height < object.y < fullscreen_resolution[1]

def shortest_angle_diff(a, b):
    diff = (b - a + 180) % 360 - 180
    return diff

class weaponclass():
    def __init__(self,x=0,y=0,rotation=0,turnspeed=20,movementspeed=5,image = None,sideoffset=20,frontoffset=5,rotationoffset = 0):
        self.x = x
        self.y = y
        self.rotation = rotation
        self.turnspeed = turnspeed
        self.movementspeed = movementspeed
        self.image = image
        self.sideoffset = sideoffset
        self.frontoffset = frontoffset
        self.rotationoffset = rotationoffset
        self.rect = pygame.Rect(x,y,image.get_width(),image.get_height())

class playerclass():
    def __init__(self,x,y,width,height,color,rotation=0,mobvisible = True):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.color = color
        self.rect = pygame.Rect(self.x,self.y,self.width,self.height)
        self.rotation = rotation
        self.mobvisible = mobvisible

class enemyclass():
    def __init__(self,x,y,image):
        self.x = x
        self.y = y
        self.width = image.get_width()
        self.height = image.get_height()
        self.image = image
        self.rect = pygame.Rect(self.x,self.y,self.width,self.height)
    
    def move(enemy):
        global dx,dy,closestenemy
        enemy.x -= dx
        enemy.y -= dy
        enemy.rect.topleft = enemy.x,enemy.y

    def target(enemy):
        global closestenemy
        if enemy.rect.colliderect(player.rect):
            closestenemy = None
            enemies.remove(enemy)

        else:
            angle = math.atan2(player.y-enemy.y,player.x-enemy.x)
            enemy.x += math.cos(angle) * enemyspeed
            enemy.y += math.sin(angle) * enemyspeed
            enemy.rect.topleft = enemy.x,enemy.y
        
    def checkbounds():
        global enemies,closestenemy,enemypadding,fullscreen_resolution
        for enemy in enemies:
            if enemy.x < -enemypadding:
                enemy.x = fullscreen_resolution[0]+enemypadding
            elif enemy.x > fullscreen_resolution[0]+enemypadding:
                enemy.x = -enemypadding
            if enemy.y < -enemypadding:
                enemy.y = fullscreen_resolution[1]+enemypadding
            elif enemy.y > fullscreen_resolution[1]+enemypadding:
                enemy.y = -enemypadding
            
            if closestenemy == None:
                closestenemy = enemy
            else:
                if inbounds(enemy):
                    if math.sqrt((player.x-enemy.x)**2 + (player.y-enemy.y)**2) < math.sqrt((player.x-closestenemy.x)**2 + (player.y-closestenemy.y)**2):
                        closestenemy = enemy
    
    def spawn():
        global enemies,enemycooldown,maxenemies,fullscreen_resolution
        if enemycooldown == 0:
            if len(enemies) < maxenemies:
                if random.randint(0,fullscreen_resolution[0]+fullscreen_resolution[1]) < fullscreen_resolution[0]:
                    enemies.append(enemyclass(random.randint(0,fullscreen_resolution[0]),-enemyimgright.get_height() if random.choice([0,1]) == 0 else fullscreen_resolution[1],enemyimgright))
                else:
                    enemies.append(enemyclass(-enemyimgright.get_width() if random.choice([0,1]) == 0 else fullscreen_resolution[0],random.randint(0,fullscreen_resolution[1]),enemyimgright))
                enemycooldown = maxenemycooldown
        else:
            enemycooldown -= 1

player = playerclass(fullscreen_resolution[0]/2-5,fullscreen_resolution[1]/2-5,10,10,(255,0,0),0)

sword1 = weaponclass(fullscreen_resolution[0]/2,fullscreen_resolution[1]/2,#coords
                      0,#rotation
                      20,#turnspeed
                      5,#movementspeed
                      swordimg,
                      20,#sideoffset
                      40,#frontoffset
                      45#rotationoffset
                      )
gun1 = weaponclass(fullscreen_resolution[0]/2,fullscreen_resolution[1]/2,
                   0,#rotation
                    15,#turnspeed
                    1,#movementspeed
                    gunimg,
                    10,#sideoffset
                    40,#frontoffset
                    90#rotationoffset
                    )
equipped = [sword1]

while True:
    eventlist = []
    mouse["x"],mouse["y"] = pygame.mouse.get_pos()
    for event in pygame.event.get():
        eventlist.append(event)
    for event in eventlist:
        if event.type == pygame.QUIT:
            pygame.quit()
        elif event.type == pygame.KEYDOWN:
            keylist.append(event.key)
        elif event.type == pygame.KEYUP:
            keylist.remove(event.key)
        elif event.type == pygame.MOUSEBUTTONDOWN:
            mouse["pressed"][event.button-1] = True
        elif event.type == pygame.MOUSEBUTTONUP:
            mouse["pressed"][event.button-1] = False

    dx = dy = 0
    for key in keylist:
        if key == 49: #1
            equipped = [sword1]
        elif key == 50: #2
            equipped = [gun1]
        elif key == 119: #w
            dy = -5
        elif key == 97: #a
            dx = -5
        elif key == 115: #s
            dy = 5
        elif key == 100: #d
            dx = 5
        elif key == 32: #space
            if invistimer == 0:
                player.mobvisible = False
                invistimer = maxinvistimer
    if invistimer > 0:
        invistimer -= 1
        if invistimer == 0:
            player.mobvisible = True
    if dx != 0 and dy != 0:
        dx *= 0.707
        dy *= 0.707
    
    for enemy in enemies:
        enemyclass.move(enemy)
    if player.mobvisible:
        for enemy in enemies:
            enemyclass.target(enemy)
        enemyclass.spawn() 
    enemyclass.checkbounds()
        
    if player.mobvisible:
        colorvalue = 255
    else:
        colorvalue = 255 * invistimer / maxinvistimer
    gamescreen.fill((colorvalue,colorvalue,colorvalue))
    if closestenemy:
        if inbounds(closestenemy) and player.mobvisible:
            pygame.draw.line(gamescreen,(0,0,0),(player.x+player.width/2,player.y+player.height/2),(closestenemy.x+closestenemy.width/2,closestenemy.y+closestenemy.height/2),2) #draw line to target
            pdy = closestenemy.y - player.y
            pdx = closestenemy.x - player.x
            player.rotation = -math.degrees(math.atan2(pdy,pdx))

        if len(equipped) > 0:
            for weapon in equipped:
                wdy = closestenemy.y - weapon.y
                wdx = closestenemy.x - weapon.x
                weapon.rotation += shortest_angle_diff(weapon.rotation, player.rotation) /100 * weapon.turnspeed

                rotated_weapon =  pygame.transform.rotate(weapon.image, weapon.rotation - weapon.rotationoffset)
                rot_rad = -math.radians(weapon.rotation)

                offset_x = math.cos(rot_rad)*weapon.frontoffset - math.sin(rot_rad)*weapon.sideoffset
                offset_y = math.sin(rot_rad)*weapon.frontoffset + math.cos(rot_rad)*weapon.sideoffset

                weapon.rect = rotated_weapon.get_rect()
                weapon.rect.center = (player.x + player.width/2 + offset_x,
                                    player.y + player.height/2 + offset_y)
        
    if player.mobvisible:
        for enemy in enemies:
            if enemy.x < player.x:
                enemy.image = enemyimgright
            else:
                enemy.image = enemyimgleft

    for enemy in enemies:
        gamescreen.blit(enemy.image,enemy.rect.topleft)

    if len(equipped) > 0:
        for weapon in equipped:
            if rotated_weapon and weapon.rect:
                gamescreen.blit(rotated_weapon, weapon.rect.topleft)
    pygame.draw.rect(gamescreen,player.color,player.rect)
    pygame.display.update()
    gameclock.tick(60)
