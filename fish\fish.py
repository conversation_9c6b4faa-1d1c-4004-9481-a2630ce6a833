import pygame,ctypes,os,colorsys
pygame.init()
os.chdir(os.path.dirname(os.path.abspath(__file__)))
fishscreen = pygame.display.set_mode((1366,768), pygame.NOFRAME)
lijst = ["dance-happy-dance-","notworking"]
photo = None
while not photo in lijst:
    photo = input(f"Available:{", ".join(lijst)}\nfoto name:\n")
picnum = 0
while True:
    try:
       testpic = pygame.image.load(f'{photo}{picnum}.png')
       picnum += 1
    except:
        picnum -=1
        break
#i put it here
windowcode = pygame.display.get_wm_info()['window']
ctypes.windll.user32.SetForegroundWindow(windowcode)        
ctypes.windll.user32.ShowWindow(windowcode, 1)
fishscreen.fill((150,150,255))
pygame.display.update()

#i put it here
window = True
while window:
    #i put it here
    for event in pygame.event.get():
        if event.type == pygame.MOUSEBUTTONDOWN:
            fishscreen.fill((0,0,255))
            print("DANCE :D")

            for i in range(0,600):
                a = i%(picnum+1)
                color = colorsys.hls_to_rgb(((15*i)%255)/255,0.5,1)
                color = (255*color[0],255*color[1],255*color[2])
                thukuna = pygame.image.load(f'{photo}{a}.png')
                thuk = pygame.transform.rotozoom(thukuna,0,768/165)
                rect = thuk.get_rect(center = (1366/2, 768/2))
                fishscreen.fill(color)
                fishscreen.blit(thuk, rect)
                pygame.display.update()
                pygame.time.delay(25)
            pygame.display.update()
            #i put it here
            pygame.time.delay(2000)
            pygame.display.quit()
            window = False