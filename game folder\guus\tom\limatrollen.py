import pygame
pygame.init()

resolution = 1366,768
gamescreen = pygame.display.set_mode(resolution)
clock = pygame.time.Clock()
id = 0

class block:
    def __init__(self,rect,show=True,touchable=True,actions=None):
        global id
        self.rect = rect
        self.show = show
        self.touchable = touchable
        self.actions = actions
        self.id = id
        id += 1
    
    def draw(self,color):
        if self.show:
            pygame.draw.rect(gamescreen,color,self.rect)
    
    def doaction(self,action):
        try:
            match action["type"]:
                case "create":
                    pass
                case "destroy":
                    pass
                case "move":
                    dx,dy = action["args"]
                    self.rect.x += dx
                    if player.rect.colliderect(self.rect):
                        player.rect.x += dx

                    self.rect.y += dy
                    if player.rect.colliderect(self.rect):
                        player.rect.y += dy
                case "scale":
                    pass
                case "mutate":
                    pass
        except Exception as e:
            print(f"Error: {e}")

    def doactions(self):
        player.rect.x = int(player.rect.x)
        player.rect.y = int(player.rect.y)
        if self.actions != None:
            if isinstance(self.actions,list):
                for action in self.actions:
                    self.doaction(action)
            else:
                self.doaction(self.actions)

class levelclass():
    def __init__(self,backgroundcolor,objectcolor,objectlist):
        self.backgroundcolor = backgroundcolor
        self.objectcolor = objectcolor
        self.objectlist = objectlist

class playerclass():
    def __init__(self,rect,show=True,flying=False):
        self.rect = rect
        self.momentum_x = 0
        self.momentum_y = 0
        self.show = show
        self.flying = flying
        self.speed = 5
        self.touchingground = False
    def move(self,movement,level):
        # Apply horizontal momentum and move on X
        if self.flying:
            self.momentum_x += movement[0] * self.speed
            self.momentum_y += movement[1] * self.speed
        else:
            if self.touchingground:
                self.momentum_x += movement[0] * self.speed
                self.momentum_x *= 0.6
                if movement[1] == -1:  # jump
                    self.momentum_y = -10
            else:
                self.momentum_y += 0.5  # gravity
                self.momentum_x += movement[0] * self.speed * 0.5
                self.momentum_x *= 0.7


        collidedobjectx = None

        # Move X and resolve collisions
        new_x = self.rect.x + self.momentum_x
        player_rect = pygame.Rect(new_x, self.rect.y, 10, 10)
        x_collision = False
        for obj in level.objectlist:
            if obj.touchable and obj.rect.colliderect(player_rect):
                x_collision = True
                collidedobjectx = obj
                if self.momentum_x > 0:
                    new_x = obj.rect.left -10
                else:
                    new_x = obj.rect.right
                self.momentum_x = 0
                

        self.rect.x = new_x

        collidedobjecty = None

        # Move Y and resolve collisions
        new_y = self.rect.y + self.momentum_y
        player_rect = pygame.Rect(self.rect.x, new_y, 10, 10)
        y_collision = False
        self.touchingground = False
        for obj in level.objectlist:
            if obj.touchable and obj.rect.colliderect(player_rect):
                y_collision = True
                collidedobjecty = obj
                if self.momentum_y > 0:  # Falling, landed on top
                    new_y = obj.rect.top - 10
                    self.touchingground = True
                else:  # Hit ceiling
                    new_y = obj.rect.bottom
                self.momentum_y = 0
                

        self.rect.y = new_y

        # Keep inside screen bounds (optional to do after collisions)
        self.rect.x = max(0, min(self.rect.x, resolution[0] - 10))
        self.rect.y = max(0, min(self.rect.y, resolution[1] - 10))
        if self.rect.y >= resolution[1] - 10:
            self.touchingground = True

        if collidedobjectx:
            currentlevel.objectlist[currentlevel.objectlist.index(collidedobjectx)].doactions()
        if collidedobjecty:
            currentlevel.objectlist[currentlevel.objectlist.index(collidedobjecty)].doactions()

levels = [levelclass((250,156,28),(150,90,20), #rect mandatory
            [
            (block(pygame.Rect(700,600,200,100),actions={"type":"move","args":[-2,1]})),
            (block(pygame.Rect(0,700,1366,100)))
            
            ])]
currentlevel = levels[0]
player = playerclass(pygame.Rect(500,500,10,10),True,False) #rect mandatory


creatingblock = None
keylist = []
while True:
    #input
    playermovementinput = [0,0]
    events = pygame.event.get()
    for event in events:
        if event.type == pygame.QUIT:
            pygame.quit()
        elif event.type == pygame.KEYDOWN:
            keylist.append(event.key)
        elif event.type == pygame.KEYUP:
            if event.key in keylist:
                keylist.remove(event.key)

    for key in keylist:
        match key:
            case pygame.K_RIGHT|pygame.K_d:
                playermovementinput[0] += 1
            case pygame.K_LEFT|pygame.K_a:
                playermovementinput[0] -= 1
            case pygame.K_UP|pygame.K_w:
                playermovementinput[1] -= 1
            case pygame.K_DOWN|pygame.K_s:
                playermovementinput[1] += 1

 
    player.move(playermovementinput,currentlevel)
    #output
    gamescreen.fill(currentlevel.backgroundcolor)
    for obj in currentlevel.objectlist:
        obj.draw(currentlevel.objectcolor)
    if player.show:
        pygame.draw.rect(gamescreen,(0,0,0),player.rect)
    pygame.display.update()  
    clock.tick(60)