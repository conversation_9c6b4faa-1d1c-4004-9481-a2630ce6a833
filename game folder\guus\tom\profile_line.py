# Install line_profiler if you haven't already:
# pip install line_profiler

import line_profiler
import dungeon_game_T  # Import your module

# Create a profiler instance
profile = line_profiler.LineProfiler()

# Wrap the function you want to profile
profile.add_function(dungeon_game_T.drawmap)  # Profile the drawmap function

# Run the wrapped function
profile.runcall(dungeon_game_T.drawmap)

# Print the results
profile.print_stats()