#importing thingies
import json
import os
import math

#default load
default = '{"name":"null","race":"null","class":"null","inventory":[],"equipped":[],"level":"0"}'
weaponlucknames = [(1,"rotten"),(15,"filthy"),(30,"broken"),(50,"normal"),(70,"strong"),(85,"magical"),(95,"holy"),(99,"divine"),(100,"perfect")]
defaultgear = [
    {"sort":"weapon","material":"wood","name":"sword","enchantments":[],"weaponluck":20,"stacksize":1,"maxstacksize":1},
    {"sort":"helmet","material":"leather","name":"helmet","enchantments":[],"weaponluck":20,"stacksize":1,"maxstacksize":1},
    {"sort":"chestplate","material":"leather","name":"chestplate","enchantments":[],"weaponluck":20,"stacksize":1,"maxstacksize":1},
    {"sort":"leggings","material":"leather","name":"leggings","enchantments":[],"weaponluck":20,"stacksize":1,"maxstacksize":1},
    {"sort":"boots","material":"leather","name":"boots","enchantments":[],"weaponluck":20,"stacksize":1,"maxstacksize":1}
    ]
allraces = ["race1","race2"]
allclasses = ["class1","class2"]
actionlist = ["quit","inventory","inv","help","stats","equip","equipped"]

#load/create save file
def load():
    print("Loading save data...")
    if not os.path.exists('savedata.txt'):
        with open('savedata.txt', 'w') as y:  
            y.write(default)
    with open('savedata.txt', 'r') as y:
        try:
            print("Save data loaded!\nStarting game...")
            return json.load(y)
        except json.JSONDecodeError:
            print("unable to load corrupted file")
            return None

#saving function
def save(savedata):
    with open('savedata.txt', 'w') as y:
        json.dump(savedata, y)

#saving and closing
def exitgame():
    global savedata
    if savedata:
        save(savedata)
        print("Exiting game...\nData saved!")
    raise SystemExit

#getting player name function
def getname():
    n = 0
    while True:
        name = input(f"Give your character a name{n*"(only letters from A-Z allowed, length 2-10)"}: ")
        if name.isalpha() and 1 < len(name) < 11:
            return name
        else:
            n = 1

def checknew():
    #tutorial for level 0 text
    if savedata["level"] == 0:
        print("LEVEL 0 TEXT TUTORIAL")
    #getting the player name
    if savedata["name"] == "null":
        savedata["name"] = getname()
        print(f"excellent choice, {savedata["name"]}!")
    #getting player race
    if savedata["race"] == "null":
        racechoice = None
        while not racechoice in allraces:
            racechoice = input(f"race yap: {", ".join(allraces)}\n")
        print(f"alright if thats what you really want...\nyou are now a {racechoice}")
        savedata["race"] = racechoice
    #getting player class
    if savedata["class"] == "null":
        classchoice = None
        while not classchoice in allclasses:
            classchoice = input(f"class yap: {", ".join(allclasses)}\n")
        print(f"your life your choice...\nyou are now a {racechoice} {classchoice}")
        savedata["class"] = classchoice
    #giving a starter pack
    if savedata["inventory"] == [] and savedata["equipped"] == []:
        print("starter gear yap")
        savedata["inventory"] = defaultgear

def inventorychange(item,inventory):
    pass


#proper weapon/armor display
def display(item):
    prefix = None
    global weaponlucknames
    for rarityset in weaponlucknames:
        if item["weaponluck"] >= rarityset[0]:
            prefix = rarityset[1]
    return f"{prefix} {item["material"]} {item["name"]}{(item["stacksize"]>1)*f" {item["stacksize"]}"}"

#starting game
savedata = load()
checknew()

#gameloop
while True:
    action = None
    while not action in actionlist:
        action = input("")
    print("-"*24)
    if action == "quit":
        quitinput = input("Exit game? T/F\n")
        if quitinput == "T":
            break
    if action == "inventory" or action == "inv":
        print("Your inventory:")
        for inv_item in savedata["inventory"]:
            print(display(inv_item))
        #EQUIPBALVHA>OEFKHEJALIKHFA
    if action == "help":
        print("available commands:")
        print(', '.join(actionlist))
    if action == "stats":
        print(f"{savedata["name"]}'s stats:\nLevel: {savedata["level"]}\nRace: {savedata["race"]}\nClass: {savedata["class"]}")
    if action == "equip" or action == "equipped":
        while equipping == True:
            print(f"Your equipment:\n{"\n".join(f"{index+1}. {display(item)}" for index, item in enumerate(savedata["equipped"]))}")
            action2 = input("U(number) to unequip. C to cancel. UA to unequip all.")
            action2 =  action2.list()
            if action2[0].upper == "U":
                if action2[1] == "A" or action2[1] == "a":
                    for item in savedata["equipped"]:
                        inventorychange(item,savedata["inventory"])
                        #REMOVE FROM EQUIPPED
                else:
                    try:
                        a = int(action2[1])
                    except ValueError:
                        break
            if action2[0].upper() == "C":
                equipping = False
        
    
    print("-"*24)
            



exitgame()