import pygame,random,math
pygame.init()

resolution = 1366,768
displayscreen = pygame.display.set_mode(resolution)
clock = pygame.time.Clock()

def distance(point1,point2):
    return math.sqrt((point1[0]-point2[0])**2+(point1[1]-point2[1])**2)

class ballclass:
    def __init__(self):
        self.x = random.randint(-5,5) + resolution[0]/2
        self.y = random.randint(-5,5) + resolution[1]/2
        self.dx = random.randint(-5,5)
        self.dy = random.randint(-5,5)
    def physics(self):
        self.x += self.dx
        self.y += self.dy
        if distance((self.x,self.y),midpoint) > (radius - radiusball):
            self.dx = - self.dx
            self.dy = - self.dy
    def draw(self):
        pygame.draw.circle(displayscreen,(255,255,255),(self.x,self.y),radiusball,)
radius = 200
radiusball = 10
balls = []
midpoint = (resolution[0]/2,resolution[1]/2)
while True:
    events = pygame.event.get()
    for event in events:
        if event.type == pygame.MOUSEBUTTONDOWN:
            balls.append(ballclass())
    
    pygame.draw.circle(displayscreen,(255,0,0),(resolution[0]/2,resolution[1]/2),radius+5,10)
    if len(balls) > 0:
        for ball in balls:
            ball.physics()
            ball.draw()
            
    pygame.display.flip()
    clock.tick(60)