import pygame,sys,random,math
from pygame.locals import *

point = (1366/2,768/2)
save = False
linelength = 50
prevcoord = False
density = 5
i = 1
up = right = True
speed = 1
clicked = False

print("Starting pygame...")
pygame.init()
itr = int(input("Points: "))
gamescreen = pygame.display.set_mode()

def move(point,pos):
    surface = pygame.display.get_surface()
    w,h = surface.get_width(), surface.get_height()
    global right,up,speed
    if point[0]>pos[0]:
        if point[1]>pos[1]:
            return point[0]-speed,point[1]-speed
        else:
            return point[0]-speed,point[1]+speed
    else:
        if point[1]>pos[1]:
            return point[0]+speed,point[1]-speed
        else:
            return point[0]+speed,point[1]+speed


def distance(point1,point2):
    if point1 != point2:
        return int(math.sqrt((point2[1]-point1[1])**2+(point2[0]-point1[0])**2))
    else:
        return 0

def draw(dtype,pos):
    if dtype == 1:
        gamescreen.fill((0,0,0))
        global clicked,point,speed
        clicked = True
        speed = distance(pos,point)*0.01
        point= move(point,pos)
        #surface = pygame.display.get_surface()
        #w,h = surface.get_width(), surface.get_height()
        for a in range(0,itr):
            pygame.draw.line(gamescreen, (random.randint(0,255),random.randint(0,255),random.randint(0,255)), (random.randint(-linelength,linelength)+point[0],random.randint(-linelength,linelength)+point[1]), point, 4)
        pygame.display.update()
        if save == True:
            global i 
            pygame.image.save(gamescreen, rf"C:\Users\<USER>\Desktop\Images project\{i}.jpeg")
            print(f"Image {i} saved.")
        i += 1

while True: 
    for event in pygame.event.get(): 
        if event.type == QUIT:  
           pygame.quit()
           sys.exit()
        if event.type == MOUSEBUTTONDOWN or clicked == True:
            pos = pygame.mouse.get_pos()
            draw(1,pos)