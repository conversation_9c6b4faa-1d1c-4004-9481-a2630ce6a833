#score system improve
#downslam functionality
#images/animations
#more obstacles/enemies
#gun?

#platformer infinite 2d
import pygame, sys, random, math
pygame.init()
#start variables
for i in range(1):
    #display
    resolution = (1366,768)
    GAMESCREEN = pygame.display.set_mode(resolution)
    pygame.display.set_caption("platformer voor guus")
    #variables
    highscore = 0
    baseheight = 500
    jumps = 0
    maxjumps = 2
    clock = pygame.time.Clock()
    fps = 60
    statistics = True
    effecthaswarning = ["death"]
    backgroundcyclemax = 60
    backgroundcycle = backgroundcyclemax+1
    backgrounddots = 100
    #loading images
    playbutton = pygame.image.load("playbutton.png")
    playbuttonrect = playbutton.get_rect()
    playbuttonrect.center = (0.5*resolution[0], 0.5*resolution[1])
    
    playbuttonsurface = pygame.Surface(playbuttonrect.size, pygame.SRCALPHA)
    #player images
    playerimg = pygame.image.load("player1.png")
    playerrect = playerimg.get_rect()
    playerrect.center = (resolution[0]/2,baseheight-playerrect[3]/2)
#new game variables
def setvar():
    global score,verticalspeed,jumpforce,gravity,slam,obstacles,upcomingobstacles,speed,hasrevive
    score = 0
    verticalspeed = 0
    jumpforce = 20
    gravity = 1
    slam = 0
    obstacles = []
    upcomingobstacles = []
    speed = 10
    hasrevive = 1
#get distance between two points
def distance(point1,point2):
    return math.sqrt((point1[0]-point2[0])**2+(point1[1]-point2[1])**2)
#draw menu screen (for death animation)
def showstats(stattype):
    global statistics
    if statistics == True:
        global clock,resolution,score,speed,playerrect,playing
        statisticfont = pygame.font.SysFont("arial",36)
        fpstext = statisticfont.render(f"{round(clock.get_fps())}/{fps} fps", True,(255,0,0))
        GAMESCREEN.blit(fpstext,(0,0))
        if not playing and stattype == "menu":
            speedtext = statisticfont.render(f"{round(speed*100)/100} p/f", True,(255,0,0))
            GAMESCREEN.blit(speedtext,(0,37))
        if playing and not stattype == "menu":
            scoretext = statisticfont.render(f"{round(score)}", True,(255,255,255))
            GAMESCREEN.blit(scoretext,(resolution[0]/2-50,playerrect[1]-37))
        
def drawmenuscreen():
    global GAMESCREEN,playbuttonsurface,playbutton,playbuttonrect,highscore,backgroundcycle,backgroundcyclemax
    colorvalue = round(255*math.sin(backgroundcycle/backgroundcyclemax))
    GAMESCREEN.fill((colorvalue,colorvalue,colorvalue))
    #draw playbutton
    playbuttonsurface.blit(playbutton, (0, 0))
    #playbuttonsurface.fill((30,40,70,200), special_flags=pygame.BLEND_RGBA_MULT)
    GAMESCREEN.blit(playbuttonsurface, playbuttonrect.topleft)
    highscorefont = pygame.font.SysFont("arial",36)
    highscoretext = highscorefont.render(f"Highscore: {highscore}", True,(255,0,0))
    GAMESCREEN.blit(highscoretext,(resolution[0]/2-playbuttonrect[2]/2,playbuttonrect[1]+playbuttonrect[3]))
    showstats("menu")

#draw game screen
def drawgamescreen():
    global upcomingobstacles, slam, playerimg,resolution,playerrect,obstacles,baseheight,hasrevive,effecthaswarning
    #background
    colorvalue = 255/20*slam
    GAMESCREEN.fill((colorvalue,colorvalue,colorvalue))
    #baseline
    pygame.draw.line(GAMESCREEN,(255,255,255),(0,baseheight),(1366,baseheight),5)
    #obstacle line
    for upcomingobstacle in upcomingobstacles:
        if upcomingobstacle["effect"] in effecthaswarning:
            pygame.draw.line(GAMESCREEN,(255,0,0),(0,upcomingobstacle["y"]+upcomingobstacle["height"]/2),(resolution[0],upcomingobstacle["y"]+upcomingobstacle["height"]/2),round(10*(1-upcomingobstacle["timer"]/upcomingobstacle["maxtimer"])))
    #obstacle
    for obstacle in obstacles:
        if obstacle["effect"] == "death":
            obstaclecolor = (255,0,0)
        elif obstacle["effect"] == "revive":
            obstaclecolor = (0,255,0)
        pygame.draw.rect(GAMESCREEN,obstaclecolor,(obstacle["x"],obstacle["y"],obstacle["width"],obstacle["height"]))
    #player
    GAMESCREEN.blit(playerimg,playerrect)
    #revives
    for i in range(hasrevive):
        pygame.draw.rect(GAMESCREEN,(0,255,0),(resolution[0]-25*(i+1),0,20,20))
    
    showstats("game")
        
    pygame.display.update()
#death function
def die():
    drawgamescreen()
    pygame.display.update()
    global GAMESCREEN,resolution,playing,obstacles
    GRID_SIZE = 10  # Number of rows and columns for slicing
    SPEED = 4  # Speed of breaking effect
    piece_width = resolution[0] // GRID_SIZE
    piece_height = resolution[1] // GRID_SIZE
    deathscreencopy = GAMESCREEN.copy()
    pieces = []
    for row in range(GRID_SIZE):
        if row:
            pygame.draw.line(GAMESCREEN,(255,255,255),(0,row*piece_height),(resolution[0],row*piece_height),3)
            pygame.time.wait(100)
            pygame.display.update()
    for col in range(GRID_SIZE):
        if col:
            pygame.draw.line(GAMESCREEN,(255,255,255),(col*piece_width,0),(col*piece_width,resolution[1]),3)
            pygame.time.wait(100)
            pygame.display.update()
    deathlinetimer = pygame.time.get_ticks()
    
    for row in range(GRID_SIZE):
        for col in range(GRID_SIZE):
            rect = pygame.Rect(col * piece_width, row * piece_height, piece_width, piece_height)
            piece = deathscreencopy.subsurface(rect).copy()
            if rect[0] > resolution[0]/2:
                dx = random.randint(2,5)
            else:
                dx = - random.randint(2,5)
            if rect[1] > resolution[1]/2:
                dy = random.randint(2,5)
            else:
                dy = - random.randint(2,5)
            pieces.append({"piece":piece,"x": rect.x,"y": rect.y,"speedx": dx,"speedy": dy,"x0":col*piece_width,"y0":row*piece_height})
    allpieces = []
    global hasrevive
    for piece in pieces:
        allpieces.append(piece)
    while True:
        if (pygame.time.get_ticks()-deathlinetimer) > 500:
            while pieces:
                remaining_pieces = []
                for piece in pieces:
                    piece["x"] += piece["speedx"] * SPEED  # Move X
                    piece["y"] += piece["speedy"] * SPEED  # Move Y
                    if (0-piece_width) < piece["x"] < resolution[0] and (0-piece_height) < piece["y"] < resolution[1]:
                        remaining_pieces.append(piece)
                        GAMESCREEN.blit(piece["piece"], (piece["x"], piece["y"]))
                pieces = remaining_pieces
                clock.tick(fps)
                pygame.display.update()
                drawmenuscreen()
            playing = False
            if not hasrevive:
                obstacles = []
            break
    if hasrevive:
        playing = True
        for i in range(len(allpieces)):
            piece = allpieces[i]
            GAMESCREEN.blit(piece["piece"], (piece["x0"], piece["y0"]))
            pygame.time.wait(10)
            pygame.display.update()
        obstacles = []
        global slam
        slam = 20
        pygame.time.wait(100)
        hasrevive -= 1
        return False
    else:
        return True
#game loop
playing = False
setvar()
while True:
    #get events
    events = []
    for event in pygame.event.get():
        events.append(event)
    #menu screen
    if playing == False:
        hasrevive = 0
        #draw rain
        backgroundcycle += 1
        if backgroundcycle > backgroundcyclemax:
            backgroundcycle = 0
            drawmenuscreen()
            pygame.display.update()
            if statistics == True:
                statisticfont = pygame.font.SysFont("arial",36)
                fpstext = statisticfont.render(f"{round(clock.get_fps())}/{fps} fps", True,(255,0,0))
                resolutiontext = statisticfont.render(f"{resolution[0]}x{resolution[1]}p", True,(255,0,0))
                GAMESCREEN.blit(fpstext,(0,0))
                GAMESCREEN.blit(resolutiontext,(0,37))
        #detect play button clicked
        for event in events:
            if event.type == pygame.MOUSEBUTTONDOWN:
                mouseposition =pygame.mouse.get_pos()
                pressedbuttons =  pygame.mouse.get_pressed()
                if pressedbuttons[0] == True and playbuttonrect.collidepoint(mouseposition):
                    playing = True
                    score = 0
                    speed = 10
    #playing
    elif playing == True:
        speed = speed *1.0002
        score += speed/60
        #if not touching ground
        if not playerrect[1] + playerrect[3] >= baseheight:
            playerrect[1] += verticalspeed
            onground = False
            verticalspeed += gravity
        #if touching ground
        if playerrect[1] + playerrect[3] >= baseheight:
            playerrect[1] = baseheight - playerrect[3]
            onground = True
            verticalspeed = 0
            jumps = maxjumps
        #keybinds
        for event in events:
            if event.type == pygame.KEYDOWN:
                pressedkeyname = pygame.key.name(event.key)
                print(pressedkeyname)
                #jump
                if pressedkeyname in ("space","up","w"):
                    if jumps:
                        verticalspeed = -jumpforce
                        playerrect[1] -= 2
                        jumps -= 1
                #slam
                elif pressedkeyname in ("down","s"):
                    if not onground:
                        verticalspeed += jumpforce*1.5
                        slam = 20
                #show statistics
                elif pressedkeyname in ("p"):
                    statistics = not statistics
        if obstacles or upcomingobstacles:
            for obstacle in obstacles:
                obstacle["x"] -= speed
                if obstacle["x"] + obstacle["width"] < 0:
                    obstacles.remove(obstacle)
            for upcomingobstacle in upcomingobstacles:
                upcomingobstacle["timer"] -= 1
                if upcomingobstacle["timer"] == 1:
                    obstacles.append(upcomingobstacle)
                    upcomingobstacles.remove(upcomingobstacle)
        if len(obstacles) < 1 and len(upcomingobstacles) < 1:
            obstacleheight = random.randint(50,150)
            obstaclewidth = random.randint(50,150)
            obstacletimer = random.randint(30,90)
            effect = random.choice(["death","revive"])
            upcomingobstacles.append({"maxtimer":obstacletimer,"timer":obstacletimer,"x":resolution[0]+obstaclewidth,"y":random.choice([baseheight-obstacleheight,random.randint(0,baseheight-2*obstacleheight)]),"height":obstacleheight,"width":obstaclewidth,"effect":effect})
        #death
        death = False
        for obstacle in obstacles:
            if playerrect.colliderect((obstacle["x"],obstacle["y"],obstacle["width"],obstacle["height"])):
                if obstacle["effect"] == "death":
                    death = die()
                elif obstacle["effect"] == "revive":
                    hasrevive += 1
                    obstacles.remove(obstacle)
        if death:
            if score > highscore:
                highscore = round(score)
            setvar()
            continue

        drawgamescreen()       
        if slam >0:
            slam -= 1   
    for event in events:
        if event.type == pygame.QUIT:
            pygame.quit()
            sys.exit()
    clock.tick(fps)
