def diamond(n):
    if n%2 == 0 or n <1 or n != int(n):
        return None
    else:
        returnlist = []
        for i in range(int(n/2)):
            returnlist.append(int(((n-1)/2-i))*" ")
            returnlist.append((1+2*i)*"*")
            returnlist.append(int(((n-1)/2-i))*" ")
            returnlist.append("\n")
        returnlist.append("*"*n)
        returnlist.append("\n")
        for i in range(int(n/2)):
            returnlist.append(int(((n-1)/2)-2+i)*" ")
            returnlist.append((n-2*(i+1))*"*")
            returnlist.append(int(((n-1)/2))*" ")
            returnlist.append("\n")
        return "".join(returnlist)
print(diamond(9))