import pygame,random
pygame.init()

GAMESCREEN = pygame.display.set_mode((1366, 768))
GAMESCREEN = pygame.display.set_caption("Window-1")

while True:
    for y in range(0, 768, 10):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                quit()
        for x in range(0, 1366, 10):
            pygame.draw.rect(GAMESCREEN, (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)), (x, y, 10, 10))
            pygame.display.update((x, y, 10, 10))
