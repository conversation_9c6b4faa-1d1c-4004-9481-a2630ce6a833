chants = [
{"chant": "arcane burst", "damage": [("light", "*", 3), ("pierce", "+", 2)], "mana": 18},
{"chant": "slicing frost", "damage": [("ice", "**", 1.6), ("slash", "*", 1.9)], "mana": 22},
{"chant": "celest", "damage": [("ice", "**", 1.9), ("stab", "*", 1.8)], "mana": 26},
{"chant": "Cinder Burst", "damage": [("fire", "*", 2.5), ("pierce", "**", 1.4)], "mana": 24},
{"chant": "propellant", "damage": [("air", "*", 2)], "mana": 14},
{"chant": "earthshaker", "damage": [("earth", "*", 2.3)], "mana": 15},
{"chant": "ether", "damage": [("light", "*", 2.2), ("slash", "**", 1.5)], "mana": 24},
{"chant": "Frost Nova", "damage": [("ice", "**", 1.7), ("blunt", "*", 2.2)], "mana": 22},
{"chant": "frostquake", "damage": [("ice", "*", 2.4), ("earth", "**", 1.6)], "mana": 35},
{"chant": "gale thrust", "damage": [("air", "**", 1.7), ("pierce", "*", 1.9), ("stab", "+", 2)], "mana": 21},
{"chant": "icequake", "damage": [("ice", "**", 2.3), ("earth", "*", 1.9)], "mana": 33},
{"chant": "infernal fang", "damage": [("fire", "*", 2), ("pierce", "**", 1.6), ("stab", "+", 3)], "mana": 22},
{"chant": "levitating droplet", "damage": [("water","**",2.6)], "mana": 20},
{"chant": "Lunar Surge", "damage": [("light", "*", 2.4), ("earth", "**", 1.5)], "mana": 29},
{"chant": "molten crash", "damage": [("fire", "**", 2.2), ("blunt", "+", 6), ("earth", "*", 1.8)], "mana": 35},
{"chant": "radiant piercer", "damage": [("light", "**", 1.8), ("pierce", "*", 2), ("blunt", "+", 2), ("slash", "*", 1.2)], "mana": 28},
{"chant": "recoil", "damage": [("stab", "*", 1.4), ("slash", "**", 1.7)], "mana": 12},
{"chant": "scale of the dragon", "damage": [("slash", "+", 3), ("pierce", "*", 1.5)], "mana": 18},
{"chant": "shadow fury", "damage": [("dark", "**", 1.8), ("blunt", "*", 1.5)], "mana": 18},
{"chant": "shadow stab", "damage": [("dark", "*", 1.9), ("stab", "**", 1.4)], "mana": 10},
{"chant": "Shattered Earth", "damage": [("earth", "*", 2.7), ("blunt", "**", 1.6)], "mana": 30},
{"chant": "solar flare", "damage": [("fire", "**", 2.5), ("light", "*", 1.8)], "mana": 32},
{"chant": "Solar Beam", "damage": [("light", "**", 2.2), ("ice", "*", 1.6)], "mana": 25},
{"chant": "tempest rush", "damage": [("air", "*", 1.8), ("slash", "*", 1.6), ("blunt", "+", 3)], "mana": 23},
{"chant": "Tempest Strike", "damage": [("air", "**", 1.8), ("blunt", "*", 2.5)], "mana": 20},
{"chant": "tidal fang", "damage": [("water", "**", 1.5), ("slash", "*", 2)], "mana": 20},
{"chant": "Tidal Crush", "damage": [("water", "*", 2.1), ("earth", "**", 1.5)], "mana": 28},
{"chant": "twin meteors", "damage": [("fire", "**", 1.5), ("slash", "*", 2)], "mana": 25},
{"chant": "vortex shock", "damage": [("air", "*", 1.7), ("light", "**", 1.5)], "mana": 20},
{"chant": "void grasp", "damage": [("dark", "*", 1.8), ("ice", "**", 2)], "mana": 28},
{"chant": "Void Wave", "damage": [("dark", "*", 1.9), ("pierce", "*", 2.2)], "mana": 27},
{"chant": "whirlwind fury", "damage": [("air", "*", 2.1), ("dark", "**", 1.5)], "mana": 22},
{"chant": "wild eruption", "damage": [("earth", "*", 2.5), ("fire", "**", 1.4)], "mana": 30},
{"chant": "whispering breeze", "damage": [("air", "*", 2.2), ("stab", "**", 1.7)], "mana": 19},
{"chant": "dark torrent", "damage": [("dark", "*", 2.1), ("water", "**", 1.8)], "mana": 23},
{"chant": "stabbing mist", "damage": [("air", "**", 1.9), ("stab", "*", 2.3)], "mana": 20},
{"chant": "void cyclone", "damage": [("dark", "*", 2.3), ("air", "*", 1.8)], "mana": 21},
{"chant": "piercing wave", "damage": [("water", "*", 2.4), ("stab", "**", 1.5)], "mana": 22},
{"chant": "shadow stretch", "damage": [("dark", "**", 2.2), ("air", "*", 2)], "mana": 25},
{"chant": "tidal stab", "damage": [("water", "*", 2.1), ("stab", "*", 2.4)], "mana": 18},
{"chant": "wind cleaver", "damage": [("air", "*", 2.3), ("stab", "+", 2)], "mana": 20},
{"chant": "dark whirlwind", "damage": [("dark", "*", 2.1), ("air", "**", 1.7)], "mana": 24},
{"chant": "abyssal strike", "damage": [("dark", "**", 1.8), ("stab", "*", 2.2)], "mana": 22},
{"chant": "duality", "damage": [("water", "**", 1.3), ("fire", "*", 2.2)], "mana": 18},
{"chant": "oxidant", "damage": [("fire", "**", 1.8), ("air", "*", 2.2)], "mana": 27},
{"chant": "reflection", "damage": [("ice", "**", 1.8), ("light", "*", 2.2), ("water","*",2.2)], "mana": 22},
{"chant": "icicle", "damage": [("pierce", "**", 1.3), ("ice", "*", 3)], "mana": 32}
]
spelllist = [
    'arcane burst', 'slicing frost', 'celest', 'Cinder Burst', 'propellant', 'earthshaker',
    'ether', 'Frost Nova', 'frostquake', 'gale thrust', 'icequake', 'infernal fang',
    'levitating droplet', 'Lunar Surge', 'molten crash', 'radiant piercer', 'recoil',
    'scale of the dragon', 'shadow fury', 'shadow stab', 'Shattered Earth', 'solar flare',
    'Solar Beam', 'tempest rush', 'Tempest Strike', 'tidal fang', 'Tidal Crush',
    'twin meteors', 'vortex shock', 'void grasp', 'Void Wave', 'whirlwind fury',
    'wild eruption', 'whispering breeze', 'dark torrent', 'stabbing mist',
    'void cyclone', 'piercing wave', 'shadow stretch', 'tidal stab', 'wind cleaver',
    'dark whirlwind', 'abyssal strike', 'duality', 'oxidant', 'reflection', 'icicle'
]

highestspells = [{"value":0,"chants":None,"mana":0}]
import random
def randomspell():
    randomspell = []
    randommanacost = 0
    while randommanacost <= maxmana:
        r = spelllist[random.randint(0,len(spelllist)-1)]
        if not r in randomspell:
            randomspell.append(r)
            for a in chants:
                if a["chant"] == randomspell[-1]:
                    randommanacost += a["mana"]
    randomspell.pop(-1)
    randomspell.append("cast")
    return randomspell

maxmana = 100
endings = ["cast"]

def getdamage(total):
    highestdamage = 0
    highestelement = None
    for element in total:
        value = total[element]
        if value > highestdamage:
            highestdamage = value
            highestelement = element
    return (round(highestdamage*10))/10,highestelement

def endspell():
    return [],{"water":0,"fire":0,"earth":0,"air":0,"slash":0,"light":0,"dark":0,"pierce":0,"stab":0,"blunt":0,"ice":0},maxmana,maxmana

currentchant,total,mana,prevmana = endspell()

def cycle(val):
    global currentchant,total,mana,prevmana
    chanting = val
    validchant = 0
    for chant in chants:
        if chant["chant"] == chanting:
            if not chanting in currentchant:
                validchant = 1
            else:
                validchant = -1
            for damageblock in chant["damage"]:
                if damageblock[1] == "+":
                    total[damageblock[0]] += damageblock[2]
                elif damageblock[1] == "*":
                    total[damageblock[0]] = total[damageblock[0]] * damageblock[2]
                elif damageblock[1] == "**":
                    total[damageblock[0]] = total[damageblock[0]] ** damageblock[2]
            prevmana = mana
            mana -= chant["mana"]
            if mana < 0:
                currentchant,total,mana,prevmana = endspell()
                print("you ran out of mana and the spell was cancelled")
            else:
                #print(f"Mana {prevmana} -> {mana} [{chanting}]")
                currentchant.append(chanting)

    damage,element = getdamage(total)
    if validchant == 0:            
        if chanting in endings:
            global highestspells
            if len(highestspells) < 30:
                highestspells.append({"value":damage,"chants":chanting,"mana":maxmana-mana})
            else:
                for top in highestspells:
                    if top["value"] < damage:
                        top["value"] = damage
                        top["chants"] = currentchant
                        break
            highestspells = sorted(highestspells, key=lambda d: d['value'])
            #print(f"you cast your spell and deal {damage} {element} damage")
            currentchant,total,mana,prevmana = endspell()
        else:    
            print(f"you failed the chant and took {damage} {element} damage")
            print(chanting)
            currentchant,total,mana,prevmana = endspell()
    elif validchant == -1:
        print(f"you got cocky and took {damage} {element} damage")
        currentchant,total,mana,prevmana = endspell()

for u in range(1000000):
    if u%10000 == 0:
        print(u)
    randomspelllist = randomspell()
    #print(randomspelllist)
    for i in range(0,len(randomspelllist)):
        val = randomspelllist[i]
        cycle(val)

chantcount = {}

for spell in highestspells:
    print(f"{30-highestspells.index(spell)}) {spell["value"]} {spell["chants"]}-----------{spell["mana"]} mana cost")
    for chant in spell["chants"]:
        if chant in chantcount:
            chantcount[chant] += 1
        else:
            chantcount[chant] = 1
chantcount = sorted(chantcount.items(), key=lambda x:x[1])
for item in chantcount:
    print(item)