import pygame, random, math
pygame.init()

resolution = 1366, 768
gamescreen = pygame.display.set_mode(resolution)
gameclock = pygame.time.Clock()

# Increase tile size to 30x30
TILE_SIZE = 30

# Add loading screen
font = pygame.font.SysFont(None, 36)
loading_text = font.render("Generating world...", True, (255, 255, 255))
loading_rect = loading_text.get_rect(center=(resolution[0]/2, resolution[1]/2))
gamescreen.fill((0, 0, 0))
gamescreen.blit(loading_text, loading_rect)
pygame.display.update()

def coinflip(weight):
    if random.randint(0,1000) < weight*1000:
        return True
    else:
        return False

class cameraclass():
    def __init__(self,x,y,scale):
        self.x = x
        self.y = y
        self.scale = scale

class playerclass():
    def __init__(self,x,y,width,height):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.rect = pygame.Rect(self.x,self.y,self.width,self.height)
        self.color = (255,0,0)
        self.speed = 5
    def draw(self):
        pygame.draw.rect(gamescreen,self.color,
                        (round(self.x-camera.x*TILE_SIZE),
                         round(self.y-camera.y*TILE_SIZE),
                         self.width,self.height))
    def move(self,dx,dy):
        self.x += dx
        self.y += dy
        if not 0 <= self.x <= background.width*TILE_SIZE-self.width:
            if dx < 0:
                self.x = round(self.x/TILE_SIZE) * TILE_SIZE
            else:
                self.x = round(self.x/TILE_SIZE) * TILE_SIZE
        if not 0 <= self.y <= background.height*TILE_SIZE-self.height:
            if dy < 0:
                self.y = round(self.y/TILE_SIZE) * TILE_SIZE 
            else:
                self.y = round(self.y/TILE_SIZE) * TILE_SIZE

        self.rect.topleft = self.x,self.y

class backgroundclass():
    def __init__(self,width,height):
        self.width = width
        self.height = height
        self.tiles = [[0 for _ in range(width)] for _ in range(height)]
        
        for y in range(height):
            for x in range(width):
                value = random.choice([1,2,3,4,5])/5 if coinflip((math.sin(x/10) + math.cos(y/10)+2)/4) else 0
                self.tiles[y][x] = max(0,min(1,value))
    
    def draw(self):
        # Calculate the visible range of tiles
        start_y = max(0, int(camera.y) - 2)
        end_y = min(self.height, int(camera.y) + resolution[1]//TILE_SIZE + 4)
        
        start_x = max(0, int(camera.x) - 2)
        end_x = min(self.width, int(camera.x) + resolution[0]//TILE_SIZE + 4)
        
        # Fill the entire screen with green first
        gamescreen.fill((0, 255, 0))
        
        # Draw only the visible tiles
        for y in range(start_y, end_y):
            for x in range(start_x, end_x):
                # Calculate screen position
                screen_x = int(x*TILE_SIZE - camera.x*TILE_SIZE)
                screen_y = int(y*TILE_SIZE - camera.y*TILE_SIZE)
                
                # Draw the tile
                tilevalue = self.tiles[y][x]
                tilecolor = (255*tilevalue, 255*tilevalue, 255*tilevalue)
                pygame.draw.rect(gamescreen, 
                                tilecolor,
                               (screen_x, screen_y, TILE_SIZE+1, TILE_SIZE+1))

# Create a smaller world for better performance
background = backgroundclass(200, 200)
last_time = 0
player = playerclass(0,0,TILE_SIZE,TILE_SIZE)  # Adjust player size to match tiles
camera = cameraclass(0,0,1) # scale not used
max_camera_y = background.height - resolution[1]/TILE_SIZE
max_camera_x = background.width - resolution[0]/TILE_SIZE
keylist = []
while True:
    current_time = pygame.time.get_ticks()
    dt = (current_time - last_time) / 1000.0  # Convert to seconds
    last_time = current_time
    deltafactor = dt * 60
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            pygame.quit()
        elif event.type == pygame.KEYDOWN:
            keylist.append(event.key)
        elif event.type == pygame.KEYUP:
            if event.key in keylist:
                keylist.remove(event.key)

    cammove = False
    movex = movey = 0
    for key in keylist:
        match key:
            case pygame.K_w:
                movey -= 1
                cammove = True
            case pygame.K_s:
                movey += 1
                cammove = True
            case pygame.K_a:
                movex -= 1
                cammove = True
            case pygame.K_d:
                movex += 1
                cammove = True
    if movex != 0 and movey != 0:
        movex *= 0.707
        movey *= 0.707

    movex *= deltafactor * player.speed
    movey *= deltafactor * player.speed
    player.move(movex,movey)
    
        
    if cammove:
        camera.x = (player.x+player.width/2)/TILE_SIZE - resolution[0]/2/TILE_SIZE
        camera.y = (player.y+player.width/2)/TILE_SIZE - resolution[1]/2/TILE_SIZE
        
        # Ensure camera stays within bounds
        camera.y = max(0, min(camera.y, max_camera_y))
        camera.x = max(0, min(camera.x, max_camera_x))

    background.draw()
    player.draw()
    
    pygame.display.update()
    gameclock.tick(60)
