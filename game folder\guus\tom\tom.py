import pygame,random
pygame.init()

resolution = (1366,768)
GAMESCREEN = pygame.display.set_mode(resolution)
playerdata = {"score":0,"x":0,"y":resolution[1]/2}
gameclock = pygame.time.Clock()
fpslimit = 60
playing = False

pbwidth = 200
pbheight = 100
playbuttonrect = pygame.rect.Rect(resolution[0]/2-pbwidth/2,resolution[1]/2-pbheight/2,pbwidth,pbheight)
lanes = 4
linetimer = 200
emptyheight = 100
cars = []#size 200x100
carimg1 = pygame.image.load('car1.png').convert()
carimg2 = pygame.image.load('car2.png').convert()
carimg3 = pygame.image.load('car3.png').convert()
carrect = carimg1.get_rect()
speed = 10
speedvar = 3
totcaramount = 5
carid = 0
distance = 50


def drawroadlines():
    global linetimer,GAMESCREEN
    for i in range(lanes-1):
        lineheight = emptyheight + (i+1)* (resolution[1]-2*emptyheight)/lanes
        pygame.draw.line(GAMESCREEN,(255,255,0),(0,lineheight),(resolution[0],lineheight),4)

    pygame.draw.line(GAMESCREEN,(255,255,255),(0,emptyheight),(resolution[0],emptyheight),10)
    pygame.draw.line(GAMESCREEN,(255,255,255),(0,resolution[1]-emptyheight),(resolution[0],resolution[1]-emptyheight),10)

while True:
    events = pygame.event.get()
    mousepos = pygame.mouse.get_pos()
    if playing:
        pygame.draw.rect(GAMESCREEN,(20,20,20),(0,emptyheight,resolution[0],resolution[1]-2*emptyheight))
        drawroadlines()
        new_cars = []
        laneoccupation = [False,False,False,False]
        for car in cars:
            car["x"] -= car["speed"]
            if car["x"] >= -200:
                new_cars.append(car)
                carrect.topleft = car["x"], car["y"]
                match car["color"]:
                    case 0:
                        GAMESCREEN.blit(carimg1, carrect)
                    case 1:
                        GAMESCREEN.blit(carimg2, carrect)
                    case 2:
                        GAMESCREEN.blit(carimg3, carrect)
            if car["x"] >= resolution[0]-500:
                laneoccupation[int((car["y"]-121)/142)] = True
            for othercar in cars:
                if not car["id"] == othercar["id"]:
                    if car["y"] == othercar["y"]:
                        if abs(car["x"] - othercar["x"]) < (200+distance):  
                            car["speed"] = othercar["speed"] = (car["speed"]+othercar["speed"])/2

        cars = new_cars

            
        for event in events:
            if event.type == pygame.KEYDOWN:
                pass
        if False in laneoccupation:
            if len(cars) < totcaramount:
                chosenlane = False
                while not chosenlane:
                    chosenlanes = random.randint(0,3)
                    if not laneoccupation[chosenlanes]:
                        chosenlane = True
                createdcar = {"id":carid,"x":resolution[0],"y":(121+142*chosenlanes),"color":random.randint(0,2),"speed":speed+random.randint(-speedvar,speedvar)}
                carid += 1
                cars.append(createdcar)
    else:
        for event in events:
            if event.type == pygame.MOUSEBUTTONDOWN:
                if playbuttonrect.collidepoint(mousepos):
                    playing = True
                    cars = []
        GAMESCREEN.fill((0,100,0))
        pygame.draw.rect(GAMESCREEN,(20,20,20),(0,emptyheight,resolution[0],resolution[1]-2*emptyheight))
        drawroadlines()
        pygame.draw.rect(GAMESCREEN,(255,255,255),playbuttonrect)


    pygame.display.update()
    gameclock.tick(fpslimit)