{"tasks": [{"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "C:\\mingw-w64\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}], "version": "2.0.0"}