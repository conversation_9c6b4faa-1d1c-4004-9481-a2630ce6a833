import pygame,sys,random
from pygame.locals import *
print("Starting pygame...")
pygame.init()
itr = "cheese"
while itr.isnumeric() == False:
    itr = input("Points: ")
itr = int(itr)
gamescreen = pygame.display.set_mode()
pygame.display.set_caption("nice shape window")
pygame.mouse.set_system_cursor(pygame.SYSTEM_CURSOR_HAND)

while True:
    pygame.display.update()  
    for event in pygame.event.get(): 
        if event.type == QUIT:  
           pygame.quit()
           sys.exit()
        if event.type == pygame.MOUSEBUTTONDOWN:
            gamescreen.fill((0,0,0))
            pos=pygame.mouse.get_pos()
            listofpoints = [pos]
            for i in range(0,itr):
                surface = pygame.display.get_surface()
                w,h = surface.get_width(), surface.get_height()
                listofpoints.append((random.randint(0,w-1),random.randint(0,h-1)))
            for point in listofpoints:
                for otherpoint in listofpoints:
                    pygame.draw.line(gamescreen, (255,255,255), point, otherpoint, 4)
                listofpoints.remove(point)
            