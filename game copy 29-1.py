#importing thingies
import json
import os

#default load
default = '{"name":"null","race":"null","class":"null","inventory":[],"level":"0"}'
allraces = ["race1","race2"]
allclasses = ["class1","class2"]

#load/create save file
print("Loading save data...")
if not os.path.exists('savedata.txt'):
    with open('savedata.txt', 'w') as y:  
        y.write(default)
with open('savedata.txt', 'r') as y:
    try:
        savedata = json.load(y)
        print("Save data loaded!\nStarting game...")
    except json.JSONDecodeError:
        print("unable to load corrupted file")
        savedata = None

#saving function
def save(savedata):
    with open('savedata.txt', 'w') as y:
        json.dump(savedata, y)

#saving and closing
def exitgame():
    global savedata
    if savedata:
        save(savedata)
        print("Exiting game...\nData saved!")
    raise SystemExit

#getting player name function
def getname():
    n = 0
    while True:
        name = input(f"Give your character a name{n*"(only letters from A-Z allowed, length 2-10)"}: ")
        if name.isalpha() and 1 < len(name) < 11:
            return name
        else:
            n = 1

#tutorial for level 0 text
if savedata["level"] == 0:
    print("LEVEL 0 TEXT TUTORIAL")

#getting the player name
if savedata["name"] == "null":
    savedata["name"] = getname()
    print(f"excellent choice, {savedata["name"]}!")

#getting player race
if savedata["race"] == "null":
    racechoice = None
    while not racechoice in allraces:
        racechoice = input(f"race yap: {", ".join(allraces)}\n")
    print(f"alright if thats what you really want...\nyou are now a {racechoice}")
    savedata["race"] = racechoice

#getting player class
if savedata["class"] == "null":
    classchoice = None
    while not classchoice in allclasses:
        classchoice = input(f"class yap: {", ".join(allclasses)}\n")
    print(f"your life your choice...\nyou are now a {racechoice} {classchoice}")
    savedata["class"] = classchoice

#giving a starter pack
if savedata["inventory"] == []:
    print("apes")

print(savedata)
exitgame()