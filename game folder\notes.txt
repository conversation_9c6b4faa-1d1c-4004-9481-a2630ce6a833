default = '{"name":"null","race":"null","class":"null","inventory":[],"equipped":[],"level":"0"}'
item attributes: sort,material,name,enchantments,weaponluck,stacksize,maxstacksize
equipped: sword helmet chestplate leggings boots
-----------------------
add text colors
more functions
https://www.google.com/search?q=rich+python
add enchantment display
add help commands explaination
add damage system
add equip system
(add trinkets etc)
add mana/abilities
add health
add enchantments
add stats (weaponluck)
brainstorm story idea / game type
(multiplayer?(leaderboard?)(lobbies?)(hypixel bazaar/auction house?))
add enemies (bosses)
add quests
add location/travel
add tutorial / inventory
add classes/races
(tooltips)
(implement second window(crits?))
(2d with pygame)
add item descriptions
magic chant system
dungeons
crates / lootboxes
forging
enchanting
save magic chants


liam heeft opdrach 7 van 3.2 bij engels vals gespeeld >:(
