import numpy,pygame,extrafile,time

pygame.init()

fullscreenresolution = 1366,768
#renderresolution = 200,100
renderresolution = fullscreenresolution
grid_size = 3#*7
range = (0,256)
setting = 0
grid = extrafile.generategrid(renderresolution,grid_size,range)
heightmap = extrafile.interpolate(renderresolution,grid,grid_size)
heightmap = heightmap.astype(numpy.uint8)
coloredsurface = extrafile.colorize_heightmap(renderresolution,heightmap,setting,150)
scaledsurface = pygame.transform.scale(coloredsurface,fullscreenresolution)
displayscreen = pygame.display.set_mode(fullscreenresolution)
displayscreen.blit(scaledsurface, (0, 0))
pygame.display.update()
print("done")
while True:
    mousepos = pygame.mouse.get_pos()
    coloredsurface = extrafile.colorize_heightmap(renderresolution,heightmap,setting,int(mousepos[0]/fullscreenresolution*255))
    scaledsurface = pygame.transform.scale(coloredsurface,fullscreenresolution)
    displayscreen.blit(scaledsurface, (0, 0))