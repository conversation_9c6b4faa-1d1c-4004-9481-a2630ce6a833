import pygame
pygame.init()

resolution = 1366,768
gamescreen = pygame.display.set_mode(resolution)
clock = pygame.time.Clock()

class block:
    def __init__(self,obj):
        x,y,w,h = obj["rect"]
        try:
            self.action = obj["action"]
        except KeyError:
            self.action = None
        self.rect = pygame.Rect(x,y,w,h)
    def draw(self):
        pygame.draw.rect(gamescreen,currentlevel.blockcolor,self.rect)
        
class level:
    def __init__(self,level):
        self.backgroundcolor = level["backgroundcolor"]
        self.blockcolor = level["objectcolor"]
        self.objects = []
        for obj in level["objects"]:
            self.objects.append(block(obj))
    
    def processactions(self,actions):
        if actions:
            for action in actions:
                match action:
                    case 1:
                        player.momentum_x += 50 * (1 if player.direction == False else -1)
                    case 2:
                        print(player.momentum_x)
                        
                actions.remove(action)  

    def draw(self):
        gamescreen.fill(self.backgroundcolor)

class playerclass:
    def __init__(self,rect,
                 show = True):
        self.rect = rect
        self.show = show
        self.momentum_x = 0
        self.momentum_y = 0
        self.grounded = False
        self.speed = 0.6
        self.direction = False

    def processinput(self,movementinput):
        if movementinput != [0,0]:
            inputx,inputy = movementinput
            if self.grounded:
                self.momentum_x += inputx * self.speed
                self.momentum_y -= inputy * 40
            else: #in air
                self.momentum_x += inputx * self.speed * 0.2

    def move(self):
        global activeactions

        self.momentum_y += 1 #gravity
        self.touched_x = False
        self.touched_y = False
        #drag
        if self.grounded:
            factor = 0.9
        else:
            factor = 0.98
        self.momentum_x *= factor
        self.momentum_y *= factor

        self.grounded = False

        #move with momentum:x
        self.rect.x += self.momentum_x
        if self.momentum_x > 0:
            self.direction = True
        elif self.momentum_x < 0:
            self.direction = False
            
        #check collision:x
        for obj in currentlevel.objects:
            if obj.rect.colliderect(self.rect): #check collision
                if self.momentum_x > 0:
                    self.rect.x = obj.rect.x - self.rect.width
                elif self.momentum_x < 0:
                    self.rect.x = obj.rect.x + obj.rect.width
                self.touched_x = True
                activeactions.append(obj.action)

        #check boundaries:x
        new_x = max(0,self.rect.x) #left edge
        new_x = min(new_x,resolution[0]-self.rect.width) #right edge
        if new_x != self.rect.x:
            self.touched_x = True
            self.rect.x = new_x

        #move with momentum:y
        self.rect.y += self.momentum_y

        #check collision:y
        for obj in currentlevel.objects:
            if obj.rect.colliderect(self.rect): #check collision
                if self.momentum_y > 0:
                    self.rect.y = obj.rect.y - self.rect.height
                    self.grounded = True
                elif self.momentum_y < 0:
                    self.rect.y = obj.rect.y + obj.rect.height
                self.touched_y = True
                activeactions.append(obj.action)
        
        #check boundaries:y
        new_y = max(0,self.rect.y) #top edge
        new_y = min(new_y,resolution[1]-self.rect.height) #bottom edge
        if new_y != self.rect.y:
            if self.momentum_y > 0:
                self.grounded = True
            self.touched_y = True
            self.rect.y = new_y
        


    def draw(self):
        pygame.draw.rect(gamescreen,(0,0,0),self.rect)

levels = [{"backgroundcolor":(250,156,28),
           "objectcolor":(180,100,30),
           "objects":[
                    {"rect":(0,0,1366,200),"action":None},
                    {"rect":(0,200,200,368),"action":1},
                    {"rect":(1166,200,200,368),"action":0},
                    {"rect":(0,568,1366,200),"action":None},
                    {"rect":(658,339,50,139),"action":2,}
                      ]}]

currentlevel = level(levels[0])
player = playerclass(pygame.Rect(resolution[0]/2,resolution[1]/2,20,20))

# gebruik _id voor init
activeactions = []
keylist = []
while True:
    movementinput = [0,0] #x,y

    for event in pygame.event.get():
        match event.type:
            case pygame.QUIT:
                pygame.quit()
            case pygame.KEYDOWN:
                keylist.append(event.key)
            case pygame.KEYUP:
                if event.key in keylist:
                    keylist.remove(event.key)
        
    for key in keylist:
        match key:
            case pygame.K_w|pygame.K_SPACE:
                movementinput[1] = 1
            case pygame.K_a:
                movementinput[0] -= 1
            case pygame.K_d:
                movementinput[0] += 1
    
    player.processinput(movementinput)
    player.move() #includes collision check and boundary check
    currentlevel.processactions(activeactions)
    

    
    currentlevel.draw()
    for obj in currentlevel.objects:
        obj.draw()
    player.draw()
    pygame.display.flip()
    clock.tick(60)