import random

inputlist = ["roll","equip"]


class itemclass():
    def __init__(self,nameetc,rarity):
        if len(nameetc) < 2:
            nameetc.append("No description available.")
        self.name = nameetc[0]
        self.description = nameetc[1]
        self.rarity = rarity
    def display(self):
        return f"{self.name} ({self.rarity})\n{self.description}"

currentitem = currentroll = itemclass(["None","Default item, everyone has it."],"common")
itemdict = {"common":[5,[["commonitem"],
                         ["commonitem2"]]],

            "uncommon":[10,[["uncommonitem"],
                            ["uncommonitem2"]]],

            "rare":[10,[["rareitem"],
                        ["rareitem2"]]],

            "epic":[10,[["epicitem"],
                        ["epicitem2"]]],

            "mythic":[10,[["mythicitem"],
                          ["mythicitem2"]]],
                          
            "legendary":[-1,[["legendaryitem"],
                             ["legendaryitem2"]]]}

def rollitem():
    print("rolling...")
    for rarity in itemdict:
        upgrade,values = itemdict[rarity]
        if upgrade > 0:
            if random.randint(1,upgrade) != 1:
                return itemclass(values[random.randint(0,len(values)-1)],rarity)
        else:
            return itemclass(values[random.randint(0,len(values)-1)],rarity)


while True:
    userinput = input(f"item equipped:{currentitem.display()}\nitem rolled:{currentroll.display()}\n{inputlist}:\n")
    if userinput in inputlist:
        match userinput:
            case "roll":
                currentroll = rollitem()
            case "equip":
                if currentroll:
                    currentitem = currentroll
                else:
                    pass
    else:
        print(f"{userinput} not available")