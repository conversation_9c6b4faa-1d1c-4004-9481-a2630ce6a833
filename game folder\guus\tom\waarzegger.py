import time
limalist = ["liam","lima","liem"]
denylist = ["not","niet","geen","no","n't","nt"]
tlist = ["gay","homo","bi","lgbtq","lgbt","regenboog","mannen"]
confirmations = ["yes","correct","agree","ye","yeah","y"]

def check(words, target):
    if type(words) == type("string"):
        return words.lower() in target
    else:
        state = False
        for word in words:
            if word.lower() in target:
                state = True
        return state
for i in range(1):
    confirmation = False
    while not confirmation:
        question = input("ask the fortune teller your question,\nand they will answer with Yes or No.\nYour question: ")
        confirmation = input(f"You asked:\n{question}\nconfirm question?\n")
        if confirmation.lower() in confirmations and not confirmation.lower() in denylist:
            confirmation
    print("The fortuneteller looks into their crystal ball")
    time.sleep(len(question)*0.3)
    newq = []
    for char in list(question):
        if char.isalpha:
            newq.append(char)
    question2 = "".join(newq)

    flipstate = True
    if check(limalist,question2):
        flipstate = False
    if check(tlist,question2):
        flipstate = not flipstate
    if check(denylist,question2):
        flipstate = not flipstate
    
    print(f"{"\n"*20}The fortune concludes that the statement:\n{question}\nis truthfully answered with a:")
    if flipstate:
        print("Yes")
    else:
        print("No")