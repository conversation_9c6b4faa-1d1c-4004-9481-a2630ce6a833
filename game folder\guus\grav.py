import pygame,random,math,sys
pygame.init()

runnedticks = 0
fps = 60
clock = pygame.time.Clock()
resolution = (1366,768)
speed = 3
particles = []
pvar = -1000
anchors = [{"id":1,"x":resolution[0]/2,"y":resolution[1]/2,"pull":-20},
           {"id":2,"x":(resolution[0]/2+300),"y":resolution[1]/2,"pull":pvar},
           {"id":3,"x":(resolution[0]/2-300),"y":resolution[1]/2,"pull":pvar},
           {"id":4,"x":resolution[0]/2,"y":resolution[1]/2+300,"pull":pvar},
           {"id":5,"x":resolution[0]/2,"y":resolution[1]/2-300,"pull":pvar},
           {"id":6,"x":(resolution[0]/2+200),"y":resolution[1]/2+200,"pull":pvar},
           {"id":7,"x":(resolution[0]/2-200),"y":resolution[1]/2+200,"pull":pvar},
           {"id":8,"x":(resolution[0]/2+200),"y":resolution[1]/2-200,"pull":pvar},
           {"id":9,"x":(resolution[0]/2-200),"y":resolution[1]/2-200,"pull":pvar}]
gamescreen = pygame.display.set_mode(resolution)
pulling = False
sendingrandom = False
colorchange = False
drawlines = True

def gravitate(p,a):
    dx = a["x"] - p["x"]
    dy = a["y"] - p["y"]
    dt = math.sqrt(dx**2+dy**2)
    if not dt:
        dt = 1
    dn = a["pull"]/dt
    if 0 <= dn < 0.1:
        dn = 0.1
    elif -0.1 <= dn <=0:
        dn = -0.1
    p["dx"]  += dx *dn / dt**2
    p["dy"]  += dy *dn / dt**2
    return p

while True:
    events = pygame.event.get()
    for event in events:
        if event.type == pygame.QUIT:
            pygame.quit
            sys.exit
        elif event.type == pygame.MOUSEBUTTONDOWN:
            particles = []
            mousepos = pygame.mouse.get_pos()
            if sendingrandom:
                for i in range(1000):
                    angle = random.randint(1,360)/180*math.pi
                    addedparticle = {"id":i,"x":mousepos[0],"y":mousepos[1],"dx":random.randint(-speed,speed)*math.cos(angle),"dy":random.randint(-speed,speed)*math.sin(angle)}
                    particles.append(addedparticle)
            else:
                for i in range(50):
                    angle = i/500*math.pi
                    color = (255,255,255)
                    addedparticle = {"pull":1,"color":color,"id":i,"x":mousepos[0],"y":mousepos[1],"dx":random.randint(-speed,speed)*math.cos(angle),"dy":random.randint(-speed,speed)*math.sin(angle)}
                    particles.append(addedparticle)
        elif event.type == pygame.KEYDOWN:
            pressedkeyname = pygame.key.name(event.key)
            mousepos = pygame.mouse.get_pos()
            mouseanchor = {"x":mousepos[0],"y":mousepos[1],"pull":10000}
            if pressedkeyname == "space":
                for bparticle in particles:
                    bparticle = gravitate(bparticle,mouseanchor)

    for mparticle in particles:
        mparticle["x"] += mparticle["dx"]
        mparticle["y"] += mparticle["dy"]
        if colorchange:
            colorchangevar = mparticle["color"]
            r,g,b = colorchangevar[0],colorchangevar[1],colorchangevar[2]
            currentspeed = math.sqrt(mparticle["dx"]**2+mparticle["dy"]**2)
            r = 255- (math.sqrt((mparticle["x"]-1366/2)**2+(mparticle["y"]-768/2)**2)/3.2)%255
            g = 255-r
            b = 0
            mparticle["color"] = (r,g,b)
        if pulling:
            for anchor in anchors:
                if anchor["id"] == 1:
                    anchor1 = anchor
            if -anchor1["pull"]/2 < mparticle["x"]-anchor1["x"] < anchor1["pull"]/2 and -anchor1["pull"]/2 < mparticle["y"]-anchor1["y"] < anchor1["pull"]/2:
                anchor1["pull"] += 0.2
                particles.remove(mparticle)
            else:
                for anchor in anchors:
                    mparticle = gravitate(mparticle,anchor1)
        else:
            for anchor in anchors:
                mparticle = gravitate(mparticle,anchor)
            if not 0 < mparticle["x"] < resolution[0] or not 0 < mparticle["y"] < resolution[1]:
                mparticle["x"] = resolution[0]/2 + random.randint(-5,5)
                mparticle["y"] = resolution[1]/2 + random.randint(-5,5)
                mparticle["dx"] = mparticle["dy"] = 0

            #for pparticle in particles:
                #mparticle = gravitate(mparticle,pparticle)

        
    gamescreen.fill((0,0,0))
    for sparticle in particles:
        pygame.draw.rect(gamescreen,sparticle["color"],(sparticle["x"]-5,sparticle["y"]-5,10,10))
        #pygame.draw.circle(gamescreen,(0,50,100),(sparticle["x"],sparticle["y"]),5)
        if drawlines:
            pd = 100000
            pid = None
            for s2particle in particles:
                if not s2particle["id"] == sparticle["id"]:
                    dx = s2particle["x"] - sparticle["x"]
                    dy = s2particle["y"] - sparticle["y"]
                    dt = math.sqrt(dx**2+dy**2)
                    if dt < pd:
                        pd = dt
                        pid = s2particle["id"]
            for s3particle in particles:
                if s3particle["id"] == pid:
                    pygame.draw.line(gamescreen,(0,255,0),(sparticle["x"],sparticle["y"]),(s3particle["x"],s3particle["y"]))
            pd2 = 100000
            pid2 = None
            for anchor in anchors:
                if not anchor["id"] == sparticle["id"]:
                    dx = anchor["x"] - sparticle["x"]
                    dy = anchor["y"] - sparticle["y"]
                    dt = math.sqrt(dx**2+dy**2)
                    if dt < pd2:
                        pd2 = dt
                        pid2 = anchor["id"]
            for anchorl in anchors:
                if anchorl["id"] == pid2:
                    pygame.draw.line(gamescreen,(255,0,0),(sparticle["x"],sparticle["y"]),(anchorl["x"],anchorl["y"]))
    for anchor in anchors:
        if anchor["id"] == 1:
            if pulling:
                pygame.draw.rect(gamescreen,(255,0,0),(resolution[0]/2-anchor["pull"]/2,resolution[1]/2-anchor["pull"]/2,anchor["pull"],anchor["pull"]))
            else:
                pygame.draw.rect(gamescreen,(255,0,0),(resolution[0]/2-5,resolution[1]/2-5,10,10))
        else:
            pygame.draw.rect(gamescreen,(0,255,0),(anchor["x"]-5,anchor["y"]-5,10,10))
    pygame.display.update()
    runnedticks += 1
    #pygame.image.save(gamescreen, f"C:/Users/<USER>/Desktop/proj py/game folder/guus/images2/{runnedticks}.jpeg")
    clock.tick(fps)