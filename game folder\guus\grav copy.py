import pygame,random,math,sys
pygame.init()

fps = 60
clock = pygame.time.Clock()
resolution = (1366,768)
speed = 1
particles = []
anchor = {"x":resolution[0]/2,"y":resolution[1]/2,"pull":10}
gamescreen = pygame.display.set_mode(resolution)

def gravitate(p,a):
    dx = a["x"] - p["x"]
    dy = a["y"] - p["y"]
    dt = math.sqrt(dx**2+dy**2)
    dn = a["pull"]/dt
    p["dx"]  += dx *dn / dt
    p["dy"]  += dy *dn / dt
    return p

while True:
    events = pygame.event.get()
    for event in events:
        if event.type == pygame.QUIT:
            pygame.quit
            sys.exit
        elif event.type == pygame.MOUSEBUTTONDOWN:
            particles = []
            mousepos = pygame.mouse.get_pos()
            for i in range(100):
                angle = random.randint(1,360)/180*math.pi
                addedparticle = {"id":i,"x":mousepos[0],"y":mousepos[1],"dx":random.randint(-speed,speed)*math.cos(angle),"dy":random.randint(-speed,speed)*math.sin(angle)}
                particles.append(addedparticle)
    for mparticle in particles:
        mparticle["x"] += mparticle["dx"]
        mparticle["y"] += mparticle["dy"]
        mparticle = gravitate(mparticle,anchor)

    if particles:
        totalx = totaly = 0
        strength = 100000
        mousepos = pygame.mouse.get_pos()
        for aparticle in particles:
            totalx += aparticle["x"]
            totaly += aparticle["y"]
        totalx += mousepos[0]*strength
        totaly += mousepos[1]*strength
        totalx /= (len(particles)+strength)
        totaly /= (len(particles)+strength)
        anchor["x"] = totalx
        anchor["y"] = totaly    
    
    gamescreen.fill((0,0,0))
    for sparticle in particles:
        pygame.draw.rect(gamescreen,(255,255,255),(sparticle["x"]-5,sparticle["y"]-5,10,10))
    pygame.draw.rect(gamescreen,(255,0,0),(resolution[0]/2-anchor["pull"]/2,resolution[1]/2-anchor["pull"]/2,anchor["pull"],anchor["pull"]))
    pygame.draw.rect(gamescreen,(0,255,0),(anchor["x"]-5,anchor["y"]-5,10,10))
    pygame.display.update()
    clock.tick(fps)