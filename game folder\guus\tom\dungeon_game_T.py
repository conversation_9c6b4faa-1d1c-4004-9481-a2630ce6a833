import pygame,random,math,cProfile

fullscreen_resolution = 1366,768
gamescreen = pygame.display.set_mode(fullscreen_resolution)

gameclock = pygame.time.Clock()


mouse = {"x":None,"y":None,"pressed":[False,False,False]}
keylist = []

mapsize =16*2,9*2

color2=(124,252,0)
color1=(33,23,11)
coloroffset = -20
colormult = 0.5

idleimg = pygame.image.load("manstill.png").convert()
idleflipimg = pygame.transform.flip(idleimg,True,False)
walkimg = pygame.image.load("manwalk.png").convert()
walkflipimg = pygame.transform.flip(walkimg,True,False)

weapon = {"cooldown":0,"maxcooldown":90,"damage":10,"range":100,"angle":90}

map = [[(0,random.randint(0,255)) for _ in range(mapsize[0])] for _ in range(mapsize[1])]
entities = []
effects = []
player = {"x":0,"y":0,"speed":3}
ui = [{"rect":pygame.Rect(0,0,100,100)}
      ]
updaterect = pygame.Rect(0,0,fullscreen_resolution[0],fullscreen_resolution[1])
playerdirection = True

def distance(point1,point2):
    return math.sqrt((point1[0]-point2[0])**2 + (point1[1]-point2[1])**2)

def rectupdate(rect):
    global updaterect
    if updaterect:
        updaterect = updaterect.union(rect)
    else:
        updaterect = pygame.Rect(rect)


def is_entity_in_view(entity, player, mouse, view_angle):
    player_to_mouse_dx = mouse["x"] - player["x"]
    player_to_mouse_dy = mouse["y"] - player["y"]
    player_to_mouse_angle = math.degrees(math.atan2(player_to_mouse_dy, player_to_mouse_dx))
    
    player_to_entity_dx = entity[0] - player["x"]
    player_to_entity_dy = entity[1] - player["y"]
    player_to_entity_angle = math.degrees(math.atan2(player_to_entity_dy, player_to_entity_dx))
    
    angle_diff = abs((player_to_mouse_angle - player_to_entity_angle + 180) % 360 - 180)
    
    return angle_diff <= view_angle / 2

class particle:
    def __init__(self,x,y,name,movement=None,angle=None,timer=120):
        self.x = x
        self.y = y
        self.movement = movement
        self.angle = angle
        self.timer = timer
        self.name = name

class entity:
    def __init__(self,x,y,race="zombie",direction=True):
        self.x = x
        self.y = y
        self.direction = direction
        self.race = race
    def die(self,player,cause="slash"):
        match self.race:
            case "zombie":
                match cause:
                    case "slash":
                        effects.append(particle(self.x,self.y,"zombiedeath1"))
                        effects.append(particle(self.x,self.y+16,"zombiedeath2"))
                        entities.remove(self)
        rectupdate((self.x,self.y,10,10))
    


def drawmap():
    gamescreen.fill((255,255,255))

    if not map:
        return

    xoffset,yoffset = 0,0
    
    stretchedfactor = len(map)/9 - len(map[0])/16
    if stretchedfactor > 0:
        tilesize = fullscreen_resolution[1]/len(map)
        xoffset = (fullscreen_resolution[0]-tilesize*len(map[0]))/2
    else:
        tilesize = fullscreen_resolution[0]/len(map[0])
        yoffset = (fullscreen_resolution[1]-tilesize*len(map))/2

    for y in range(len(map)):
        for x in range(len(map[y])):
            match map[y][x][0]:
                case 0:
                    tilecolor = [color1[0]+map[y][x][1]/255*(color2[0]-color1[0]),color1[1]+map[y][x][1]/255*(color2[1]-color1[1]),color1[2]+map[y][x][1]/255*(color1[2]-color2[2])]
                    for i in range(3):
                        tilecolor[i] += coloroffset
                        tilecolor[i] *= colormult
                        if tilecolor[i] < 0:
                            tilecolor[i] = 0
                        elif tilecolor[i] > 255:
                            tilecolor[i] = 255
                    tilecolor = (int(tilecolor[0]),int(tilecolor[1]),int(tilecolor[2]))
                case _:
                    tilecolor = (255,255,255)
            pygame.draw.rect(gamescreen,tilecolor,(x*tilesize+xoffset,y*tilesize+yoffset,tilesize+1,tilesize+1))

def drawentities():
    for entity in entities:
        match entity.race:
            case "zombie":
                pygame.draw.rect(gamescreen,(255,0,0),(int(entity.x),int(entity.y),10,10))
        

def draweffects():
    for effect in effects:
        if effect.name == "death":
            rectupdate((effect.x,effect.y,10,10))
            pygame.draw.rect(gamescreen,(255,0,0),(effect.x,effect.y,10,10))
            if effect.timer == 0:
                velocity = 0
            else:
                velocity = 5 * (effect.timer/120)**3
            effect.x += math.cos(math.radians(effect.angle)) * velocity
            effect.y += math.sin(math.radians(effect.angle)) * velocity
            rectupdate((effect.x,effect.y,10,10))
            

def drawplayer():
    global idleimg,walkimg,idleflipimg,walkflipimg
    if not playerdirection:
        idletemp = idleflipimg
        walktemp = walkflipimg
    else:
        idletemp = idleimg
        walktemp = walkimg

    gamescreen.blit(idletemp if not walking else (walktemp if pygame.time.get_ticks()%500 < 250 else idletemp),(player["x"],player["y"]))

def drawui():
    for element in ui:
        pygame.draw.rect(gamescreen,(0,255,255),element["rect"])

def gameloop():
    global walking, effects, mouse, eventlist,keylist,entities,updaterect
    eventlist = []
    mouse["x"],mouse["y"] = pygame.mouse.get_pos()
    for event in pygame.event.get():
        eventlist.append(event)
    for event in eventlist:
        if event.type == pygame.QUIT:
            pygame.quit()
        elif event.type == pygame.KEYDOWN:
            keylist.append(event.key)
        elif event.type == pygame.KEYUP:
            keylist.remove(event.key)
        elif event.type == pygame.MOUSEBUTTONDOWN:
            mouse["pressed"][event.button-1] = True
        elif event.type == pygame.MOUSEBUTTONUP:
            mouse["pressed"][event.button-1] = False

    horizontalmovement = verticalmovement = 0
    for pressedkeyID in keylist:
        if pressedkeyID == 119: #w
            verticalmovement -= 1
        elif pressedkeyID == 97: #a
            horizontalmovement -= 1
            playerdirection = False
        elif pressedkeyID == 115: #s
            verticalmovement += 1
        elif pressedkeyID == 100: #d
            horizontalmovement += 1
            playerdirection = True
    walking = True
    if horizontalmovement == verticalmovement == 0:
        walking = False
    if horizontalmovement != 0 and verticalmovement != 0:
        rectupdate((player["x"],player["y"],32,32))
        player["x"] += horizontalmovement * 0.707 * player["speed"]
        player["y"] += verticalmovement * 0.707 * player["speed"]
        rectupdate((player["x"],player["y"],32,32))
    else:
        rectupdate((player["x"],player["y"],32,32))
        player["x"] += horizontalmovement * player["speed"]
        player["y"] += verticalmovement * player["speed"]
        rectupdate((player["x"],player["y"],32,32))
    if player["x"] < 0:
        rectupdate((player["x"],player["y"],32,32))
        player["x"] = 0
        rectupdate((player["x"],player["y"],32,32))
    elif player["x"] > fullscreen_resolution[0]-10:
        rectupdate((player["x"],player["y"],32,32))
        player["x"] = fullscreen_resolution[0]-10
        rectupdate((player["x"],player["y"],32,32))
    if player["y"] < 0:
        rectupdate((player["x"],player["y"],32,32))
        player["y"] = 0
        rectupdate((player["x"],player["y"],32,32))
    elif player["y"] > fullscreen_resolution[1]-10:
        rectupdate((player["x"],player["y"],32,32))
        player["y"] = fullscreen_resolution[1]-10
        rectupdate((player["x"],player["y"],32,32))

    for pressedkeyID in keylist:
        if pressedkeyID == 9: #tab
            map = [[(0,random.randint(0,255)) for _ in range(mapsize[0])] for _ in range(mapsize[1])]
            rectupdate((0,0,fullscreen_resolution[0],fullscreen_resolution[1]))
        if pressedkeyID == 32: #space
            entities.append(entity(fullscreen_resolution[0]/2+random.randint(-100,100),fullscreen_resolution[1]/2+random.randint(-100,100),"zombie"))
            rectupdate((fullscreen_resolution[0]/2-100,fullscreen_resolution[1]/2-100,210,210))
    
    if mouse["pressed"][0]:
        aliveentities = []
        if weapon["cooldown"] <= 0:
            for entity in entities:
                if distance((entity.x,entity.y),(player["x"],player["y"])) < weapon["range"] and is_entity_in_view((entity.x,entity.y), player, mouse, weapon["angle"]):
                    effects.append(particle(entity.x,entity.y,"death",5,math.degrees(math.atan2(entity.y-player["y"],entity.x-player["x"])),120))
                    rectupdate((entity.x,entity.y,10,10))
                    entity.die(player)
                else:
                    aliveentities.append(entity)
            weapon["cooldown"] = weapon["maxcooldown"]
            entities = aliveentities
    neweffects = []
    for effect in effects:
        effect.timer -= 1
        if effect.timer <= 0:
            rectupdate((effect.x,effect.y,10,10))
        else:
            neweffects.append(effect)
    effects = neweffects
            

    weapon["cooldown"] -= 1

    drawmap()
    drawentities()
    draweffects()
    drawplayer()
    drawui()
    if updaterect:
        pygame.display.update(updaterect)
        updaterect = None
    else:
        pygame.time.sleep(0.0083)
    
    gameclock.tick(120)

gameloop()

cProfile.run('drawmap()', filename='drawmap_profile')
import pstats
from pstats import SortKey
p = pstats.Stats('drawmap_profile')
p.sort_stats(SortKey.CUMULATIVE).print_stats(20)
