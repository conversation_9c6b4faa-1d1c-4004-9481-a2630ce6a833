import numpy,pygame

def lerp(a, b, t):
    return a + (b - a) * t

def interpolate(renderresolution,grid,grid_size):
    width,height = renderresolution[0],renderresolution[1]
    noise = numpy.zeros((height, width))
    for y in range(height):
        for x in range(width):
            gx = x // grid_size
            gy = y // grid_size
            tx = (x % grid_size) / grid_size
            ty = (y % grid_size) / grid_size
            v00 = grid[gy][gx]
            v10 = grid[gy][gx + 1]
            v01 = grid[gy + 1][gx]
            v11 = grid[gy + 1][gx + 1]
            a = lerp(v00, v10, tx)
            b = lerp(v01, v11, tx)
            value = lerp(a, b, ty)
            noise[y][x] = value
    return noise

def generategrid(renderresolution,grid_size,range):
    grid_w = renderresolution[0] // grid_size + 2
    grid_h = renderresolution[1] // grid_size + 2
    grid = numpy.random.randint(range[0],range[1],(grid_h, grid_w))
    return grid

def colorize_heightmap(renderresolution,heightmap,setting,extravar):
    WIDTH,HEIGHT = renderresolution[0],renderresolution[1]
    img = numpy.zeros((HEIGHT, WIDTH, 3), dtype=numpy.uint8)
    for y in range(HEIGHT):
        for x in range(WIDTH):
            h = heightmap[y][x]
            if setting == 0:
                if h < extravar:
                    img[y,x] = (0,0,0)
                else:
                    img[y,x] = (255,255,255)
    return pygame.surfarray.make_surface(numpy.transpose(img, (1, 0, 2)))