import webbrowser,random,time
streak = 0

brainrot_gifs = ["https://tenor.com/nl/view/jd-vance-freaky-gif-12383307270061891675",
                 "https://tenor.com/nl/view/jd-vance-baby-bouncing-bounce-vance-gif-1676333441433477857",
                 "https://tenor.com/nl/view/jd-vance-spin-jd-vance-spin-gif-5187443738191615063",
                 "https://tenor.com/nl/view/president-gif-7337296820468278588",
                 "https://tenor.com/nl/view/joe-biden-biden-fall-biden-fall-bike-gif-12861882073539067074",
                 "https://tenor.com/nl/view/brain-rot-italian-brainrot-hayatarou-hayatarou-flamingo-flamingo-gif-12919403838980480426",
                 "https://tenor.com/nl/view/frog-gif-358126946326466778",
                 "https://tenor.com/nl/view/lebron-pigeon-ai-brainrot-construction-gif-3567584481701324703",
                 "https://tenor.com/nl/view/i-caca-meme-gif-6512364033040641420",
                 "https://tenor.com/nl/view/no-no-no-bueno-donald-trump-nah-nope-gif-16365282",
                 "https://tenor.com/nl/view/donald-trump-gif-9482710",
                 ]

def death():
    try:
        # This will open the URL in the system's default browser
        webbrowser.open(brainrot_gifs[random.randint(0,len(brainrot_gifs)-1)])
        return True
    except Exception as e:
        print(f"Error: {e}")
        return False

print("Welcome to russian roulette.")
while True:
    input("Press enter to spin the chamber...")
    if random.randint(1,6) == 1:
        print("you died...")
        time.sleep(1)
        death()
        break
    else:
        print(f"you survived this round...{f"streak:{streak}" if streak > 0 else ""}")
        streak += 1
